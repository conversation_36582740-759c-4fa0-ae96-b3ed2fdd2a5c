import { RiBookShelfFill } from "@remixicon/react";
import TableSearch from "components/Common/Toolbar/TableSearch";
import FAQPromptModal from "features/KnowledgeDatabase/components/FAQPromptModal";
import { OWNER_TYPES } from "features/KnowledgeDatabase/globals";
import { FilterInterface } from "features/KnowledgeDatabase/pages/KnowledgeBase";
import { KNOWLEDGE_BASE_ROUTE_PATH } from "features/KnowledgeDatabase/routePath";
import { useState } from "react";
import { Button, Form, Nav } from "react-bootstrap";
import { useNavigate } from "react-router-dom";
import CustomBreadcrumbs from "./CustomBreadcrumbs";
import "./styles.scss";
import UploadDropdown from "./UploadDropdown";

interface Breadcrumb {
  id: string;
  name: string;
}

interface KnowledgeBaseToolbarProps {
  setFilters: (value: FilterInterface) => void;
  filters: FilterInterface;
  breadcrumbs?: Breadcrumb[];
  owner_type: string;
  isActionsEnabled: boolean;
}

export default function KnowledgeBaseToolbar({
  setFilters,
  filters,
  breadcrumbs = [],
  owner_type,
  isActionsEnabled,
}: KnowledgeBaseToolbarProps) {
  const navigate = useNavigate();
  const [showFAQModal, setShowFAQModal] = useState(true);

  const onChangeOwnerType = (value: any) => {
    if (!value) return;
    navigate(`${KNOWLEDGE_BASE_ROUTE_PATH.KNOWLEDGE_BASE}?owner_type=${value}`);
  };

  const handleFAQPromptSave = (data: any) => {
    console.log("FAQ Prompt Data:", data);
    // TODO: Implement API call to save FAQ prompt
  };

  return (
    <div className="knowledge-base-toolbar">
      <Nav
        variant="pills"
        defaultActiveKey={OWNER_TYPES.ORGANIZATION}
        as="ul"
        className="justify-content-end"
        activeKey={owner_type}
        onSelect={onChangeOwnerType}
      >
        <Nav.Item as="li">
          <Nav.Link eventKey={OWNER_TYPES.ORGANIZATION}>
            Organisational
          </Nav.Link>
        </Nav.Item>
        <Nav.Item as="li">
          <Nav.Link eventKey={OWNER_TYPES.USER}>Personal</Nav.Link>
        </Nav.Item>
      </Nav>
      <hr className="custom-hr-divider" />
      {isActionsEnabled && (
        <div className="toolbar-wrapper d-flex flex-sm-row flex-column justify-content-sm-between justify-content-center align-items-stretch mb-3">
          <div className="toolbar-wrapper-left">
            <div className="icon d-inline-block lh-1 align-middle">
              <RiBookShelfFill className="toolbar-icon object-fit-contain" />
            </div>

            <p className="title mb-0 ms-2 d-inline-block lh-1 align-middle">
              <CustomBreadcrumbs
                breadcrumbs={breadcrumbs}
                owner_type={owner_type}
              />
            </p>

            <p className="mb-0 mt-1 description">
              This is your organisations knowledge base
            </p>
          </div>

          <div className="toolbar-wrapper-right mt-sm-0 mt-2 d-flex flex-lg-row flex-column justify-content-sm-end align-items-sm-center align-items-stretch gap-3">
            <Form
              action="#!"
              className="d-flex flex-lg-row flex-column justify-content-sm-end align-items-sm-center align-items-stretch"
              style={{ gap: "10px" }}
            >
              <TableSearch
                placeholder="Search by Name"
                setFilters={setFilters}
                filters={filters}
              />
            </Form>
            <UploadDropdown
              className="upload-dropdown"
              owner_type={owner_type}
            />
            <Button
              className="lh-1 toolbar-btn-blue bg-blue border-blue text-center ms-sm-auto fw-bold"
              onClick={() => setShowFAQModal(true)}
            >
              Create FAQ Prompt
            </Button>
          </div>
        </div>
      )}

      <FAQPromptModal
        show={showFAQModal}
        onClose={() => setShowFAQModal(false)}
        onSave={handleFAQPromptSave}
      />
    </div>
  );
}
