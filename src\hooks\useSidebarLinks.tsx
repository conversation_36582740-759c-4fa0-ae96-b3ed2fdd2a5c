import {
  RiArticleLine,
  RiBookShelfLine,
  RiCustomerService2Line,
  RiLock2Line,
} from "@remixicon/react";
import { REPORTS_ROUTE_PATH } from "features/Reports/routePath";
import { ROUTE_PATH } from "routes";
import useUserStore from "stores/user";
import useFeatureEnable from "./useFeatureEnable";
import useFeatureFlags from "./useFeatureFlags";
import { KNOWLEDGE_BASE_ROUTE_PATH } from "features/KnowledgeDatabase/routePath";

const useSidebarLinks = () => {
  const userData = useUserStore((state) => state.userInfo.user);
  const { isFeatureEnabled } = useFeatureFlags();
  const { isReportsEnabled } = useFeatureEnable();

  const sidebarLinks = [
    {
      path: userData?.is_subscription
        ? ROUTE_PATH.HOME
        : ROUTE_PATH.SUBSCRIPTIONS,
      label: "Secure Chat",
      icon: RiLock2Line,
    },
    isFeatureEnabled("REPORTS") && isReportsEnabled
      ? {
          path: REPORTS_ROUTE_PATH.BUILD_REPORTS,
          label: "Reports",
          icon: RiArticleLine,
        }
      : null,
    isFeatureEnabled("KNOWLEDGE_BASE")
      ? {
          path: KNOWLEDGE_BASE_ROUTE_PATH.KNOWLEDGE_BASE,
          label: "Knowledge Database",
          icon: RiBookShelfLine,
        }
      : null,
    {
      path: ROUTE_PATH.CONTACT_US,
      label: "Contact Us",
      icon: RiCustomerService2Line,
    },
  ].filter(Boolean);
  return [sidebarLinks];
};

export default useSidebarLinks;
